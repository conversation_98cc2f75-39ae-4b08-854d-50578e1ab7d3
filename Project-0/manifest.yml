modules:
  jira:customField:
    - key: simple-requirements-field
      name: Simple Requirements
      description: Input field for simple requirements for LLM expansion.
      type: string
      readOnly: false # Ensure it's editable
  function:
    - key: field-updated-handler # New function for the trigger
      handler: index.simpleRequirementsUpdatedHandler
    - key: openrouter-processor # Async function for OpenRouter API calls
      handler: index.openRouterProcessor
      timeoutSeconds: 900 # 15 minutes timeout for long-running OpenRouter calls
  consumer: # Async event consumer for OpenRouter processing
    - key: openrouter-queue-consumer
      queue: openrouter-processing-queue
      resolver:
        function: openrouter-processor
        method: processOpenRouterRequest
  trigger: # New trigger module
    - key: field-updated-trigger
      function: field-updated-handler
      events:
        - avi:jira:updated:issue

app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/1855cc49-eb1c-46b8-b8d7-04e25dbd5aa8
permissions: # Added permissions section
  scopes:
    - read:jira-work # To read issue details and custom fields
    - write:jira-work # To update custom fields
  external:
    fetch:
      backend:
        - https://openrouter.ai # Allow calls to OpenRouter API
