import api, { route } from '@forge/api'; // Import necessary Forge APIs
import { markdownToAdf } from 'marklassian';
import { Queue } from '@forge/events'; // Import Forge Events for async processing
import Resolver from '@forge/resolver'; // Import Forge Resolver for async event handling

// Initialize the async queue for OpenRouter processing
const openRouterQueue = new Queue({ key: 'openrouter-processing-queue' });

// Initialize resolver for async event handling
const resolver = new Resolver();

// --- Handler for Issue Update Trigger (Optimized for Simple Requirements Field) ---

// Helper to get custom field ID - uses API fallback since context may not have field info
const getCustomFieldId = async (fieldKey) => {
  try {
    // Query the API to get all fields and find our custom fields
    const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/field`);
    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch fields: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return null;
    }

    const fieldsData = await fieldsResponse.json();

    // Map field keys to field names
    const fieldNameMap = {
      'simple-requirements-field': 'Simple Requirements',
      'full-requirements-field': 'Full Requirements'
    };

    const targetFieldName = fieldNameMap[fieldKey];
    if (!targetFieldName) {
      console.error(`Unknown field key: ${fieldKey}`);
      return null;
    }

    // Find the field by name
    const foundField = fieldsData.find(f => f.name === targetFieldName);
    if (!foundField) {
      console.error(`Could not find custom field with name: ${targetFieldName}`);
      return null;
    }

    console.log(`Found field ${targetFieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
    return foundField;
  } catch (error) {
    console.error(`Error fetching custom field ID for ${fieldKey}:`, error);
    return null;
  }
};

export const simpleRequirementsUpdatedHandler = async (event, context) => {
  console.log('Issue Updated Event Received:', JSON.stringify(event));
  console.log('Context:', JSON.stringify(context));

  const issueId = event.issue.id;
  const issueKey = event.issue.key;

  console.log(`Processing issue ${issueKey} (ID: ${issueId})`);

  // 1. Get Custom Field information using API
  const simpleReqField = await getCustomFieldId('simple-requirements-field');
  if (!simpleReqField) {
    console.error('Could not resolve Simple Requirements field information');
    return;
  }

  // 2. Check if the Simple Requirements field was actually updated in this event
  const changelog = event.changelog;
  if (!changelog || !changelog.items) {
    console.log(`No changelog found in event. Skipping.`);
    return;
  }

  const simpleReqFieldUpdated = changelog.items.some(item =>
    item.fieldId === simpleReqField.id || item.field === 'Simple Requirements'
  );

  if (!simpleReqFieldUpdated) {
    console.log(`Simple Requirements field was not updated in this event. Skipping.`);
    return;
  }

  console.log(`Simple Requirements field was updated. Processing...`);

  // 2. Get OpenRouter API Key from Environment Variables
  const apiKey = process.env.SECRET_OPENROUTER_API_KEY;
  if (!apiKey) {
    console.error('OpenRouter API Key (SECRET_OPENROUTER_API_KEY) is not set in Forge environment variables. Ensure it was set using `forge variables set --encrypt SECRET_OPENROUTER_API_KEY`.');
    return;
  }

  // 3. Get Full Requirements field information using API
  const fullReqField = await getCustomFieldId('full-requirements-field');

  if (!fullReqField) {
     console.error(`Could not resolve Full Requirements field information: ${fullReqField}`);
     return;
  }
  console.log(`Field IDs - Simple: ${simpleReqField.id}, Full: ${fullReqField.id}`);
  console.log(`Field Types - Simple: ${simpleReqField.schema?.type}, Full: ${fullReqField.schema?.type}`);

  // 4. Get the new value of the Simple Requirements field from the changelog
  const simpleReqChange = changelog.items.find(item =>
    item.fieldId === simpleReqField.id || item.field === 'Simple Requirements'
  );

  const simpleRequirements = simpleReqChange?.to || '';
  console.log(`Simple Requirements Input: ${simpleRequirements}`);

  if (!simpleRequirements || simpleRequirements.trim() === '') {
    console.log(`Simple Requirements field is empty for issue ${issueKey}. Skipping.`);
    return;
  }

  // 5. Queue OpenRouter API call for async processing (avoids 25-second timeout)
  console.log(`Queueing OpenRouter API call for async processing to avoid timeout...`);

  try {
    const jobId = await openRouterQueue.push({
      issueId,
      issueKey,
      simpleRequirements,
      fullReqFieldId: fullReqField.id,
      apiKey
    });

    console.log(`Successfully queued OpenRouter processing job ${jobId} for issue ${issueKey}`);
    console.log(`The Full Requirements field will be updated asynchronously in the background.`);
  } catch (error) {
    console.error(`Error queueing OpenRouter API call:`, error);
    return;
  }
  };

// --- Async Event Consumer for OpenRouter API Processing ---

// Define the async event consumer that processes OpenRouter API calls
resolver.define('processOpenRouterRequest', async ({ payload, context }) => {
  console.log('Processing OpenRouter request asynchronously:', JSON.stringify(payload));
  console.log('Async context:', JSON.stringify(context));

  const { issueId, issueKey, simpleRequirements, fullReqFieldId, apiKey } = payload;

  // Create the prompt for OpenRouter API
  const prompt = `Expand the following simple requirements into detailed, structured, actionable user story requirements:\n\n"${simpleRequirements}"\n\nFormat the output using Markdown. Reply only with Markdown Requirements Output not wrapped in a code block with no extra respone components.`;

  let fullRequirements = '';
  const timeoutDuration = 30000; // 30 seconds timeout (we have up to 15 minutes now)

  try {
    console.log(`Calling OpenRouter API with DeepSeek V3 for issue ${issueKey}`);

    // Create a timeout promise to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`OpenRouter API request timed out after ${timeoutDuration/1000} seconds`)), timeoutDuration);
    });

    // Create the actual API request promise
    const apiRequestPromise = api.fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://project0jiraplugin.atlassian.net', // Optional: for analytics
        'X-Title': 'Jira Requirements Expander', // Optional: for analytics
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-chat-v3-0324:free', // DeepSeek V3 0324 free model
        messages: [
          { role: 'system', content: 'You are a helpful assistant that expands requirements for Jira user stories.' },
          { role: 'user', content: prompt },
        ],
        temperature: 0.7, // Adjust creativity
        max_tokens: 500, // Adjust response length limit
      }),
    });

    // Race between the API request and timeout
    const openRouterResponse = await Promise.race([apiRequestPromise, timeoutPromise]);

    if (!openRouterResponse.ok) {
      const errorBody = await openRouterResponse.text();
      console.error(`OpenRouter API request failed with ${openRouterResponse.status} ${openRouterResponse.statusText}`);
      console.error('OpenRouter Error Body:', errorBody);

      // Return an InvocationError to trigger retry
      const { InvocationError, InvocationErrorCode } = await import('@forge/events');
      return new InvocationError({
        retryAfter: 60, // Retry after 60 seconds
        retryReason: InvocationErrorCode.FUNCTION_UPSTREAM_RATE_LIMITED,
        retryData: { issueKey, attempt: (payload.retryContext?.retryCount || 0) + 1 }
      });
    }

    const openRouterData = await openRouterResponse.json();

    if (openRouterData.choices && openRouterData.choices.length > 0 && openRouterData.choices[0].message) {
      fullRequirements = openRouterData.choices[0].message.content.trim();
      console.log('OpenRouter Response Received (Markdown Format):', fullRequirements);
    } else {
      console.error('OpenRouter response format unexpected or empty.', JSON.stringify(openRouterData));

      // Return an InvocationError to trigger retry
      const { InvocationError, InvocationErrorCode } = await import('@forge/events');
      return new InvocationError({
        retryAfter: 30, // Retry after 30 seconds
        retryReason: InvocationErrorCode.FUNCTION_RETRY_REQUEST,
        retryData: { issueKey, error: 'Invalid response format' }
      });
    }
  } catch (error) {
    console.error(`Error calling OpenRouter API:`, error);

    // Return an InvocationError to trigger retry for retryable errors
    if (error.message.includes('timeout') || error.message.includes('network')) {
      const { InvocationError, InvocationErrorCode } = await import('@forge/events');
      return new InvocationError({
        retryAfter: 120, // Retry after 2 minutes for timeout/network errors
        retryReason: InvocationErrorCode.FUNCTION_RETRY_REQUEST,
        retryData: { issueKey, error: error.message }
      });
    }

    // For non-retryable errors, just log and return
    console.error(`Non-retryable error for issue ${issueKey}:`, error);
    return;
  }

  console.log(`Converting Markdown to ADF format for issue ${issueKey}...`);

  // Convert the Markdown response to ADF format
  const adfContent = markdownToAdf(fullRequirements);

  console.log(`Updating issue ${issueKey} field ${fullReqFieldId} with ADF content...`);

  // Use the standard issue update API with ADF format
  const updateResponse = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`, {
    method: 'PUT',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      fields: { [fullReqFieldId]: adfContent } // ADF object
    })
  });

  if (!updateResponse.ok) {
    const errorBody = await updateResponse.text();
    console.error(`Failed to update issue ${issueKey}: ${updateResponse.status} ${updateResponse.statusText}`);
    console.error('Update Error Body:', errorBody);

    // Return an InvocationError to trigger retry for Jira API failures
    const { InvocationError, InvocationErrorCode } = await import('@forge/events');
    return new InvocationError({
      retryAfter: 60, // Retry after 60 seconds
      retryReason: InvocationErrorCode.FUNCTION_RETRY_REQUEST,
      retryData: { issueKey, error: 'Jira API update failed' }
    });
  } else {
    console.log(`Successfully updated 'Full Requirements' field (${fullReqFieldId}) for issue ${issueKey} with rich text formatted content.`);
  }
});

// Export the resolver handler for the async event consumer
export const openRouterProcessor = resolver.getDefinitions();

