# Async Queue Solution for 25-Second Timeout Issue

## Problem Solved
This implementation solves the 25-second timeout limitation in Forge apps when making OpenRouter API calls that can take longer than the execution limit.

## Solution Overview
Instead of making the OpenRouter API call synchronously in the trigger handler, we now:

1. **Immediately queue** the OpenRouter API request using Forge Async Events API
2. **Process the request asynchronously** in a background consumer with up to 15 minutes timeout
3. **Update the Jira field** once the OpenRouter API responds
4. **Handle retries automatically** with exponential backoff for failed requests

## Architecture Changes

### Before (Synchronous - Limited to 25 seconds)
```
Jira Field Update → Trigger Handler → OpenRouter API Call → Update Jira Field
                                    ↑ 
                              TIMEOUT RISK (25s limit)
```

### After (Asynchronous - Up to 15 minutes)
```
Jira Field Update → Trigger Handler → Queue Event → Return Immediately
                                           ↓
                    Background Consumer → OpenRouter API Call → Update Jira Field
                                        ↑
                                   15 MINUTE LIMIT
```

## Key Benefits

1. **No More Timeouts**: The trigger handler returns immediately after queuing
2. **Longer Processing Time**: Async consumers can run up to 15 minutes (900 seconds)
3. **Automatic Retries**: Built-in retry mechanism with exponential backoff
4. **Better User Experience**: Jira field updates don't block the UI
5. **Robust Error Handling**: Handles rate limits, network issues, and API failures

## Implementation Details

### New Components Added:

1. **Async Queue**: `openrouter-processing-queue`
2. **Async Consumer**: `processOpenRouterRequest` 
3. **Retry Logic**: Automatic retries with different strategies for different error types
4. **Extended Timeout**: 30-second initial timeout with up to 15-minute total processing time

### Retry Strategy:
- **Rate Limit Errors**: Retry after 60 seconds
- **Network/Timeout Errors**: Retry after 2 minutes  
- **Invalid Response**: Retry after 30 seconds
- **Jira API Failures**: Retry after 60 seconds
- **Maximum Retries**: 4 attempts per event

### Error Handling:
- Comprehensive logging for debugging
- Different retry strategies based on error type
- Graceful degradation for non-retryable errors

## Files Modified:

1. **`manifest.yml`**: Added async consumer and function definitions
2. **`package.json`**: Added `@forge/events` dependency
3. **`src/resolvers/index.js`**: Refactored to use async queue
4. **`src/index.js`**: Added export for async processor

## Usage

The solution is transparent to end users. When a Simple Requirements field is updated:

1. The trigger fires immediately and queues the OpenRouter processing
2. The user sees immediate feedback that processing has been queued
3. The Full Requirements field gets updated asynchronously in the background
4. If there are any failures, the system automatically retries

## Monitoring

Check the Forge logs to monitor:
- Queue job creation: `Successfully queued OpenRouter processing job {jobId}`
- Async processing: `Processing OpenRouter request asynchronously`
- Successful completion: `Successfully updated 'Full Requirements' field`
- Retry attempts: Look for `InvocationError` logs with retry information

## Deployment

1. Install dependencies: `npm install`
2. Deploy the app: `forge deploy`
3. The async queue will be automatically available after deployment

## Testing

The async nature means testing requires:
1. Update a Simple Requirements field
2. Check logs for queue job creation
3. Wait for background processing (can take up to several minutes)
4. Verify Full Requirements field is updated
5. Check logs for successful completion or retry attempts
